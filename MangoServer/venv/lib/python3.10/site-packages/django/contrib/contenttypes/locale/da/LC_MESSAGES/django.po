# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON> <<EMAIL>>, 2014,2019
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <PERSON><PERSON> <<EMAIL>>, 2012
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2019-09-17 18:00+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Danish (http://www.transifex.com/django/django/language/da/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: da\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Content Types"
msgstr "Indholdstyper"

msgid "python model class name"
msgstr "klassenavn i Python-model"

msgid "content type"
msgstr "indholdstype"

msgid "content types"
msgstr "indholdstyper"

#, python-format
msgid "Content type %(ct_id)s object has no associated model"
msgstr "Indholdstype %(ct_id)s-objekt har ingen tilhørende model"

#, python-format
msgid "Content type %(ct_id)s object %(obj_id)s doesn’t exist"
msgstr "Indholdstype %(ct_id)s-objekt %(obj_id)s findes ikke"

#, python-format
msgid "%(ct_name)s objects don’t have a get_absolute_url() method"
msgstr " %(ct_name)s-objekter har ikke en get_absolute_url()-metode"
