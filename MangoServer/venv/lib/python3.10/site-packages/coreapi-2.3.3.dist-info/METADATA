Metadata-Version: 2.0
Name: coreapi
Version: 2.3.3
Summary: Python client library for Core API.
Home-page: https://github.com/core-api/python-client
Author: <PERSON>
Author-email: <EMAIL>
License: BSD
Platform: UNKNOWN
Classifier: Development Status :: 3 - Alpha
Classifier: Environment :: Web Environment
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: BSD License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 2
Classifier: Programming Language :: Python :: 2.7
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.3
Classifier: Programming Language :: Python :: 3.4
Classifier: Programming Language :: Python :: 3.5
Classifier: Programming Language :: Python :: 3.6
Classifier: Topic :: Internet :: WWW/HTTP
Requires-Dist: coreschema
Requires-Dist: requests
Requires-Dist: itypes
Requires-Dist: uritemplate

UNKNOWN


