[2025-07-28 09:30:09,287] [ERROR] __init__ 66739 6244724736 报错提示：您的缓存信息未从执行器同步过来，请点击执行器：发送缓存数据按钮
[2025-07-29 17:30:20,293] [ERROR] __init__ 43044 6248689664 报错提示：您的缓存信息未从执行器同步过来，请点击执行器：发送缓存数据按钮
[2025-07-29 17:37:19,942] [ERROR] __init__ 43044 6299168768 报错提示：您的缓存信息未从执行器同步过来，请点击执行器：发送缓存数据按钮
[2025-07-29 17:43:11,741] [ERROR] __init__ 43044 6282342400 报错提示：您的缓存信息未从执行器同步过来，请点击执行器：发送缓存数据按钮
[2025-07-29 17:43:15,503] [ERROR] __init__ 43044 6282342400 报错提示：您的缓存信息未从执行器同步过来，请点击执行器：发送缓存数据按钮
[2025-07-29 17:44:43,891] [INFO] consumers 43044 6265516032 发送的用户：test1
发送的客户端类型：控制端
发送的数据：None
[2025-07-29 17:45:58,492] [ERROR] __init__ 43044 6248689664 报错提示：您的缓存信息未从执行器同步过来，请点击执行器：发送缓存数据按钮
[2025-07-29 17:46:03,651] [ERROR] __init__ 43044 6282342400 报错提示：您的缓存信息未从执行器同步过来，请点击执行器：发送缓存数据按钮
[2025-07-29 17:46:10,976] [ERROR] __init__ 43044 6248689664 报错提示：您的缓存信息未从执行器同步过来，请点击执行器：发送缓存数据按钮
[2025-07-29 17:46:33,037] [ERROR] __init__ 43044 6299168768 报错提示：您的缓存信息未从执行器同步过来，请点击执行器：发送缓存数据按钮
[2025-07-29 17:50:15,068] [ERROR] __init__ 43044 6282342400 报错提示：您的缓存信息未从执行器同步过来，请点击执行器：发送缓存数据按钮
[2025-07-29 17:50:25,692] [ERROR] __init__ 43044 6265516032 报错提示：您的缓存信息未从执行器同步过来，请点击执行器：发送缓存数据按钮
[2025-07-29 17:50:37,514] [ERROR] __init__ 43044 6265516032 报错提示：您的缓存信息未从执行器同步过来，请点击执行器：发送缓存数据按钮
[2025-07-29 17:55:07,670] [ERROR] __init__ 43044 6248689664 报错提示：您的缓存信息未从执行器同步过来，请点击执行器：发送缓存数据按钮
[2025-07-30 16:14:08,961] [ERROR] __init__ 83982 6251835392 报错提示：您的缓存信息未从执行器同步过来，请点击执行器：发送缓存数据按钮
[2025-07-30 16:14:15,575] [ERROR] __init__ 83982 6218182656 错误内容：ApiCaseDetailed matching query does not exist.-错误详情：Traceback (most recent call last):
  File "/Users/<USER>/Downloads/芒果自动化测试平台/个人提供代码/V3.0版本/MangoPlatform/MangoServer/PyAutoTest/tools/decorator/error_response.py", line 34, in wrapper
    return func(self, request, *args, **kwargs)
  File "/Users/<USER>/Downloads/芒果自动化测试平台/个人提供代码/V3.0版本/MangoPlatform/MangoServer/PyAutoTest/tools/view/model_crud.py", line 88, in put
    instance=self.model.objects.get(pk=request.data.get('id')),
  File "/Users/<USER>/Downloads/芒果自动化测试平台/个人提供代码/V3.0版本/MangoPlatform/MangoServer/venv/lib/python3.10/site-packages/django/db/models/manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/Users/<USER>/Downloads/芒果自动化测试平台/个人提供代码/V3.0版本/MangoPlatform/MangoServer/venv/lib/python3.10/site-packages/django/db/models/query.py", line 637, in get
    raise self.model.DoesNotExist(
PyAutoTest.auto_test.auto_api.models.ApiCaseDetailed.DoesNotExist: ApiCaseDetailed matching query does not exist.

[2025-07-30 16:14:15,580] [ERROR] __init__ 83982 6218182656 报错提示：缓存的key未填写，请先到系统管理->系统设置中填写邮箱配置
[2025-07-30 16:14:27,749] [ERROR] __init__ 83982 6218182656 错误内容：ApiCaseDetailed matching query does not exist.-错误详情：Traceback (most recent call last):
  File "/Users/<USER>/Downloads/芒果自动化测试平台/个人提供代码/V3.0版本/MangoPlatform/MangoServer/PyAutoTest/tools/decorator/error_response.py", line 34, in wrapper
    return func(self, request, *args, **kwargs)
  File "/Users/<USER>/Downloads/芒果自动化测试平台/个人提供代码/V3.0版本/MangoPlatform/MangoServer/PyAutoTest/tools/view/model_crud.py", line 88, in put
    instance=self.model.objects.get(pk=request.data.get('id')),
  File "/Users/<USER>/Downloads/芒果自动化测试平台/个人提供代码/V3.0版本/MangoPlatform/MangoServer/venv/lib/python3.10/site-packages/django/db/models/manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/Users/<USER>/Downloads/芒果自动化测试平台/个人提供代码/V3.0版本/MangoPlatform/MangoServer/venv/lib/python3.10/site-packages/django/db/models/query.py", line 637, in get
    raise self.model.DoesNotExist(
PyAutoTest.auto_test.auto_api.models.ApiCaseDetailed.DoesNotExist: ApiCaseDetailed matching query does not exist.

[2025-07-30 16:14:27,754] [ERROR] __init__ 83982 6218182656 报错提示：缓存的key未填写，请先到系统管理->系统设置中填写邮箱配置
[2025-07-30 16:17:43,035] [ERROR] __init__ 83982 6268661760 报错提示：您需要执行的用例没有对应的测试环境
[2025-07-30 16:17:50,115] [ERROR] __init__ 83982 6235009024 错误内容：ApiCaseDetailed matching query does not exist.-错误详情：Traceback (most recent call last):
  File "/Users/<USER>/Downloads/芒果自动化测试平台/个人提供代码/V3.0版本/MangoPlatform/MangoServer/PyAutoTest/tools/decorator/error_response.py", line 34, in wrapper
    return func(self, request, *args, **kwargs)
  File "/Users/<USER>/Downloads/芒果自动化测试平台/个人提供代码/V3.0版本/MangoPlatform/MangoServer/PyAutoTest/tools/view/model_crud.py", line 88, in put
    instance=self.model.objects.get(pk=request.data.get('id')),
  File "/Users/<USER>/Downloads/芒果自动化测试平台/个人提供代码/V3.0版本/MangoPlatform/MangoServer/venv/lib/python3.10/site-packages/django/db/models/manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/Users/<USER>/Downloads/芒果自动化测试平台/个人提供代码/V3.0版本/MangoPlatform/MangoServer/venv/lib/python3.10/site-packages/django/db/models/query.py", line 637, in get
    raise self.model.DoesNotExist(
PyAutoTest.auto_test.auto_api.models.ApiCaseDetailed.DoesNotExist: ApiCaseDetailed matching query does not exist.

[2025-07-30 16:17:50,122] [ERROR] __init__ 83982 6235009024 报错提示：缓存的key未填写，请先到系统管理->系统设置中填写邮箱配置
